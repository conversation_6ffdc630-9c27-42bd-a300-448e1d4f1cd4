<template>
  <view>
    <!-- 地址输入框 -->
    <view class="address-input" @click="showSelector">
      <text class="address-text">{{ selectedAddress || placeholder }}</text>
      <u-icon name="arrow-right" size="24" color="#999"></u-icon>
    </view>

    <!-- 地址选择弹窗 -->
    <u-popup v-model="show" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
      <view class="address-selector">
        <view class="selector-header">
          <text class="selector-title">选择地址</text>
          <view class="selector-close" @click="closeSelector">
            <u-icon name="close" size="24" color="#666"></u-icon>
          </view>
        </view>

        <view class="address-options">
          <view class="option-item" @click="useCurrentLocation">
            <u-icon name="map" size="32" color="#3cc51f"></u-icon>
            <text class="option-text">使用当前位置</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>

          <view class="option-item" @click="chooseFromMap">
            <u-icon name="search" size="32" color="#3cc51f"></u-icon>
            <text class="option-text">地图选点</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>

          <view class="option-item" @click="addNewAddress">
            <u-icon name="plus" size="32" color="#3cc51f"></u-icon>
            <text class="option-text">新增地址</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>

        <view class="saved-addresses" v-if="addressList.length > 0">
          <view class="section-title">已保存地址</view>
          <view class="address-list">
            <view class="address-item" v-for="address in addressList" :key="address.addressId"
              @click="selectAddress(address)">
              <view class="address-info">
                <view class="address-header">
                  <text class="address-name">{{ address.addressName }}</text>
                  <u-tag v-if="address.isDefault === '1'" text="默认" type="primary" size="mini"></u-tag>
                </view>
                <text class="address-detail">{{ address.addressDetail }}</text>
                <text class="address-contact">{{ address.contactName }} {{ address.contactPhone }}</text>
              </view>
              <u-icon name="arrow-right" size="24" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <view class="empty-state" v-else>
          <u-icon name="map" size="80" color="#ddd"></u-icon>
          <text class="empty-text">暂无保存的地址</text>
          <text class="empty-tip">点击上方选项添加地址</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getAddressList, getCurrentLocation, chooseLocation } from '@/api/address'

export default {
  name: 'AddressSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择地址'
    }
  },
  data() {
    return {
      show: false,
      addressList: [],
      selectedAddress: this.value
    }
  },
  watch: {
    value(newVal) {
      this.selectedAddress = newVal
    }
  },
  mounted() {
    this.loadAddressList()
  },
  methods: {
    // 显示选择器
    showSelector() {
      this.show = true
      this.loadAddressList()
    },

    // 关闭选择器
    closeSelector() {
      this.show = false
    },

    // 加载地址列表
    async loadAddressList() {
      try {
        const res = await getAddressList()
        this.addressList = res.data || []
      } catch (error) {
        console.error('加载地址列表失败:', error)
      }
    },

    // 使用当前位置
    async useCurrentLocation() {
      try {
        uni.showLoading({ title: '获取位置中...' })
        const location = await getCurrentLocation()
        this.emitChange({
          address: location.address,
          longitude: location.longitude,
          latitude: location.latitude
        })
        this.closeSelector()
      } catch (error) {
        console.error('获取当前位置失败:', error)
        uni.showToast({
          title: '获取位置失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 地图选点
    async chooseFromMap() {
      try {
        this.closeSelector()
        const location = await chooseLocation()
        this.emitChange({
          address: location.name || location.address,
          longitude: location.longitude,
          latitude: location.latitude
        })
      } catch (error) {
        if (error.type !== 'cancel') {
          console.error('地图选点失败:', error)
        }
      }
    },

    // 选择已保存的地址
    selectAddress(address) {
      this.emitChange({
        address: address.addressDetail,
        longitude: address.longitude,
        latitude: address.latitude,
        addressId: address.addressId
      })
      this.closeSelector()
    },

    // 新增地址
    addNewAddress() {
      this.closeSelector()
      uni.navigateTo({
        url: '/pages/address/edit'
      })
    },

    // 发送变更事件
    emitChange(data) {
      this.selectedAddress = data.address
      this.$emit('input', data.address)
      this.$emit('change', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.address-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  .address-text {
    font-size: 30rpx;
    color: #333;
  }
}

.address-selector {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .selector-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .selector-close {
      padding: 10rpx;
    }
  }

  .address-options {
    padding: 20rpx 40rpx;

    .option-item {
      display: flex;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #f8f8f8;

      &:last-child {
        border-bottom: none;
      }

      .option-text {
        flex: 1;
        margin-left: 20rpx;
        font-size: 30rpx;
        color: #333;
      }

      &:active {
        background: #f8f8f8;
      }
    }
  }

  .saved-addresses {
    .section-title {
      padding: 20rpx 40rpx 10rpx;
      font-size: 28rpx;
      color: #666;
      background: #f8f8f8;
    }

    .address-list {
      max-height: 400rpx;
      overflow-y: auto;

      .address-item {
        display: flex;
        align-items: center;
        padding: 30rpx 40rpx;
        border-bottom: 1rpx solid #f8f8f8;

        &:last-child {
          border-bottom: none;
        }

        .address-info {
          flex: 1;

          .address-header {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;

            .address-name {
              font-size: 30rpx;
              color: #333;
              margin-right: 10rpx;
            }
          }

          .address-detail {
            display: block;
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
            margin-bottom: 5rpx;
          }

          .address-contact {
            font-size: 24rpx;
            color: #999;
          }
        }

        &:active {
          background: #f8f8f8;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 80rpx 40rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
      margin: 20rpx 0 10rpx;
    }

    .empty-tip {
      font-size: 24rpx;
      color: #ccc;
    }
  }
}
</style>
