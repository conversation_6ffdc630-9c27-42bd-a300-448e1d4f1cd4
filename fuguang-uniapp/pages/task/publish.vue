<template>
  <view class="publish-container">
    <scroll-view class="form-scroll" scroll-y>
      <view class="form-container">
        <!-- 任务标题 -->
        <view class="form-item">
          <text class="label">任务标题 *</text>
          <u-input v-model="form.taskTitle" placeholder="请输入任务标题" maxlength="50" :clearable="true" />
          <text class="char-count">{{ form.taskTitle.length }}/50</text>
        </view>

        <!-- 任务描述 -->
        <view class="form-item">
          <text class="label">任务描述 *</text>
          <u-textarea v-model="form.taskDesc" placeholder="请详细描述任务内容、要求等" maxlength="500" height="200" />
          <text class="char-count">{{ form.taskDesc.length }}/500</text>
        </view>

        <!-- 任务金额 -->
        <view class="form-item">
          <text class="label">任务金额 *</text>
          <view class="amount-input-container">
            <u-input v-model="form.taskAmount" type="number" placeholder="请输入金额" :clearable="true" class="amount-input">
              <template #suffix>
                <text class="currency-suffix">RMB</text>
              </template>
            </u-input>
          </view>
          <text class="tip">建议设置合理的金额以吸引更多人接取</text>
        </view>

        <!-- 任务地址 -->
        <view class="form-item">
          <text class="label">任务地址 *</text>
          <AddressSelector v-model="form.taskAddress" placeholder="请选择任务地址" @change="onAddressChange" />
          <text class="tip">可选择已保存地址或重新定位</text>
        </view>

        <!-- 任务类型选择 -->
        <view class="form-item">
          <text class="label">任务类型 *</text>
          <view class="type-selector">
            <view class="type-row">
              <text class="type-label">一级类型：</text>
              <view class="type-input" @click="showFirstTypePicker = true">
                <text class="type-text" :class="{ 'placeholder': !selectedFirstTypeName }">
                  {{ selectedFirstTypeName || '请选择一级类型' }}
                </text>
                <u-icon name="arrow-right" size="24" color="#999"></u-icon>
              </view>
            </view>

            <view class="type-row" v-if="form.firstTypeId">
              <text class="type-label">二级类型：</text>
              <view class="type-input" @click="showSecondTypePicker = true">
                <text class="type-text" :class="{ 'placeholder': !selectedSecondTypeName }">
                  {{ selectedSecondTypeName || '请选择二级类型' }}
                </text>
                <u-icon name="arrow-right" size="24" color="#999"></u-icon>
              </view>
            </view>
          </view>

          <!-- 一级类型选择器 -->
          <u-picker :show="showFirstTypePicker" :columns="firstTypeColumns" @confirm="onFirstTypeConfirm"
            @cancel="showFirstTypePicker = false"></u-picker>

          <!-- 二级类型选择器 -->
          <u-picker :show="showSecondTypePicker" :columns="secondTypeColumns" @confirm="onSecondTypeConfirm"
            @cancel="showSecondTypePicker = false"></u-picker>
        </view>

        <!-- 图片上传 -->
        <view class="form-item">
          <text class="label">任务图片</text>
          <view class="image-upload">
            <view class="image-list">
              <view class="image-item" v-for="(image, index) in form.images" :key="index">
                <image :src="image.url" mode="aspectFill" class="uploaded-image" @click="previewImage(index)"></image>
                <view class="delete-btn" @click="deleteImage(index)">
                  <u-icon name="close" size="16" color="#fff"></u-icon>
                </view>
              </view>
              <view class="upload-btn" @click="chooseImage" v-if="form.images.length < 9">
                <u-icon name="plus" size="40" color="#999"></u-icon>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <text class="tip">最多可上传9张图片，支持jpg、png格式</text>
          </view>
        </view>

        <!-- 紧急程度 -->
        <view class="form-item">
          <text class="label">紧急程度</text>
          <u-radio-group v-model="form.urgentLevel" placement="row">
            <u-radio label="0" name="普通">普通</u-radio>
            <u-radio label="1" name="紧急">紧急</u-radio>
            <u-radio label="2" name="非常紧急">非常紧急</u-radio>
          </u-radio-group>
          <text class="tip" v-if="form.urgentLevel !== '0'">紧急任务会优先展示，建议提高金额</text>
        </view>

        <!-- 时间设置 -->
        <view class="form-item">
          <text class="label">开始时间</text>
          <view class="time-input" @click="showStartTimePicker = true">
            <text class="time-text" :class="{ 'placeholder': !startTimeText }">
              {{ startTimeText || '请选择开始时间' }}
            </text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>

          <!-- 开始时间选择器 -->
          <u-datetime-picker v-model="form.startTime" :show="showStartTimePicker" mode="datetime" :min-date="minDate"
            @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false"></u-datetime-picker>
        </view>

        <view class="form-item">
          <text class="label">结束时间</text>
          <view class="time-input" @click="showEndTimePicker = true">
            <text class="time-text" :class="{ 'placeholder': !endTimeText }">
              {{ endTimeText || '请选择结束时间' }}
            </text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>

          <!-- 结束时间选择器 -->
          <u-datetime-picker v-model="form.endTime" :show="showEndTimePicker" mode="datetime"
            :min-date="form.startTime || minDate" @confirm="onEndTimeConfirm"
            @cancel="showEndTimePicker = false"></u-datetime-picker>
        </view>

        <!-- 温馨提示 -->
        <view class="tips-card">
          <view class="tips-title">温馨提示</view>
          <view class="tips-content">
            <text class="tip-item">• 请确保任务描述真实详细</text>
            <text class="tip-item">• 任务金额一经发布不可修改</text>
            <text class="tip-item">• 恶意发布虚假任务将被封号</text>
            <text class="tip-item">• 建议合理设置任务时间</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部发布按钮 -->
    <view class="publish-bar">
      <u-button type="primary" size="large" :loading="publishing" :disabled="!canPublish" @click="handlePublish">
        {{ isEdit ? '保存修改' : '发布任务' }}
      </u-button>
    </view>
  </view>
</template>

<script>
import { publishTask, updateTask, getTaskDetail } from '@/api/task'
import { getFirstLevelTaskTypes, getChildrenTaskTypes } from '@/api/taskType'
import { uploadFile } from '@/api/upload'
import { checkLogin, getCurrentLocation } from '@/utils/common'
import AddressSelector from '@/components/AddressSelector/AddressSelector.vue'

export default {
  components: {
    AddressSelector
  },
  data() {
    return {
      isEdit: false,
      taskId: '',
      form: {
        taskTitle: '',
        taskDesc: '',
        taskAmount: '',
        taskAddress: '',
        taskType: '0',
        firstTypeId: '',
        secondTypeId: '',
        urgentLevel: '0',
        startTime: '',
        endTime: '',
        longitude: '',
        latitude: '',
        images: []
      },
      publishing: false,
      minDate: new Date().getTime(),

      // 任务类型相关
      firstTypes: [],
      secondTypes: [],
      showFirstTypePicker: false,
      showSecondTypePicker: false,
      selectedFirstTypeName: '',
      selectedSecondTypeName: '',

      // 时间选择相关
      showStartTimePicker: false,
      showEndTimePicker: false,

      // 图片上传相关
      uploading: false,


    }
  },

  computed: {
    canPublish() {
      return this.form.taskTitle &&
        this.form.taskDesc &&
        this.form.taskAmount &&
        this.form.taskAddress &&
        this.form.firstTypeId &&
        !this.publishing
    },

    startTimeText() {
      return this.form.startTime ? this.formatDateTime(this.form.startTime) : ''
    },

    endTimeText() {
      return this.form.endTime ? this.formatDateTime(this.form.endTime) : ''
    },

    firstTypeColumns() {
      return [this.firstTypes.map(item => ({ text: item.typeName, value: item.typeId }))]
    },

    secondTypeColumns() {
      return [this.secondTypes.map(item => ({ text: item.typeName, value: item.typeId }))]
    }
  },

  onLoad(options) {
    if (!checkLogin()) {
      uni.navigateBack()
      return
    }

    // 加载任务类型数据
    this.loadTaskTypes()

    if (options.id) {
      this.isEdit = true
      this.taskId = options.id
      this.loadTaskDetail()
    } else {
      this.getCurrentLocation()
    }
  },

  methods: {
    async getCurrentLocation() {
      try {
        const location = await getCurrentLocation()
        this.form.longitude = location.longitude.toString()
        this.form.latitude = location.latitude.toString()
        this.form.taskAddress = location.address || ''
      } catch (error) {
        console.error('获取位置失败:', error)
      }
    },

    async loadTaskDetail() {
      try {
        const res = await getTaskDetail(this.taskId)
        const task = res.data

        this.form = {
          taskTitle: task.taskTitle,
          taskDesc: task.taskDesc,
          taskAmount: task.taskAmount.toString(),
          taskAddress: task.taskAddress,
          taskType: task.taskType,
          startTime: task.startTime,
          endTime: task.endTime,
          longitude: task.longitude,
          latitude: task.latitude
        }
      } catch (error) {
        console.error('加载任务详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.form.taskAddress = res.name || res.address
          this.form.longitude = res.longitude.toString()
          this.form.latitude = res.latitude.toString()
        },
        fail: (error) => {
          console.error('选择位置失败:', error)
        }
      })
    },

    onStartTimeConfirm(value) {
      this.form.startTime = value
      this.showStartTimePicker = false
    },

    onEndTimeConfirm(value) {
      this.form.endTime = value
      this.showEndTimePicker = false
    },

    async handlePublish() {
      if (!this.canPublish) return

      // 验证金额
      const amount = parseFloat(this.form.taskAmount)
      if (isNaN(amount) || amount <= 0) {
        uni.showToast({
          title: '请输入有效的金额',
          icon: 'none'
        })
        return
      }

      // 验证时间
      if (this.form.startTime && this.form.endTime) {
        if (new Date(this.form.endTime) <= new Date(this.form.startTime)) {
          uni.showToast({
            title: '结束时间必须晚于开始时间',
            icon: 'none'
          })
          return
        }
      }

      this.publishing = true
      try {
        const data = {
          ...this.form,
          taskAmount: amount
        }

        if (this.isEdit) {
          data.taskId = this.taskId
          await updateTask(data)
          uni.showToast({
            title: '修改成功',
            icon: 'success'
          })
        } else {
          await publishTask(data)
          uni.showToast({
            title: '发布成功',
            icon: 'success'
          })
        }

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('发布任务失败:', error)
      } finally {
        this.publishing = false
      }
    },

    formatDateTime(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}`
    },

    // 加载任务类型数据
    async loadTaskTypes() {
      try {
        const res = await getFirstLevelTaskTypes()
        this.firstTypes = res.data || []
      } catch (error) {
        console.error('加载任务类型失败:', error)
      }
    },

    // 一级类型选择确认
    async onFirstTypeConfirm(value) {
      const selectedType = this.firstTypes.find(item => item.typeId === value[0])
      if (selectedType) {
        this.form.firstTypeId = selectedType.typeId
        this.selectedFirstTypeName = selectedType.typeName

        // 重置二级类型
        this.form.secondTypeId = ''
        this.selectedSecondTypeName = ''
        this.secondTypes = []

        // 加载二级类型
        try {
          const res = await getChildrenTaskTypes(selectedType.typeId)
          this.secondTypes = res.data || []
        } catch (error) {
          console.error('加载二级类型失败:', error)
        }
      }
      this.showFirstTypePicker = false
    },

    // 二级类型选择确认
    onSecondTypeConfirm(value) {
      const selectedType = this.secondTypes.find(item => item.typeId === value[0])
      if (selectedType) {
        this.form.secondTypeId = selectedType.typeId
        this.selectedSecondTypeName = selectedType.typeName
      }
      this.showSecondTypePicker = false
    },

    // 选择图片
    chooseImage() {
      const remainCount = 9 - this.form.images.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadImages(res.tempFilePaths)
        }
      })
    },

    // 上传图片
    async uploadImages(filePaths) {
      this.uploading = true
      try {
        for (let filePath of filePaths) {
          const uploadRes = await uploadFile(filePath)
          if (uploadRes.code === 200) {
            this.form.images.push({
              url: uploadRes.data.url,
              name: uploadRes.data.fileName,
              size: uploadRes.data.size
            })
          }
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      } finally {
        this.uploading = false
      }
    },

    // 删除图片
    deleteImage(index) {
      this.form.images.splice(index, 1)
    },

    // 预览图片
    previewImage(index) {
      const urls = this.form.images.map(img => img.url)
      uni.previewImage({
        current: index,
        urls: urls
      })
    },

    // 地址变更处理
    onAddressChange(data) {
      this.form.taskAddress = data.address
      this.form.longitude = data.longitude ? data.longitude.toString() : ''
      this.form.latitude = data.latitude ? data.latitude.toString() : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.form-scroll {
  flex: 1;
}

.form-container {
  padding: 40rpx;
}

.form-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .label {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .char-count {
    display: block;
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }

  .tip {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

.amount-input-container {
  .amount-input {
    /deep/ .u-input__content {
      border: 1rpx solid #e5e5e5;
      border-radius: 12rpx;
      padding: 0 20rpx;
    }

    .currency-suffix {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
      margin-right: 10rpx;
    }
  }
}

.location-input {
  .u-input {
    background: #f8f8f8;
  }
}

.time-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;

  .time-text {
    font-size: 30rpx;
    color: #333;

    &.placeholder {
      color: #999;
    }
  }
}

.tips-card {
  background: #fff7f0;
  border-radius: 20rpx;
  padding: 30rpx;
  border-left: 8rpx solid #ff6b35;

  .tips-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 20rpx;
  }

  .tips-content {
    .tip-item {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10rpx;
    }
  }
}

.publish-bar {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

// 任务类型选择样式
.type-selector {
  .type-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .type-label {
      width: 140rpx;
      font-size: 28rpx;
      color: #666;
      flex-shrink: 0;
    }

    .type-input {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      cursor: pointer;

      .type-text {
        font-size: 30rpx;
        color: #333;

        &.placeholder {
          color: #999;
        }
      }
    }
  }
}

// 图片上传样式
.image-upload {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;

    .image-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .uploaded-image {
        width: 100%;
        height: 100%;
      }

      .delete-btn {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-btn {
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed #ddd;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .upload-text {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }

      &:active {
        background: #f0f0f0;
      }
    }
  }
}
</style>
